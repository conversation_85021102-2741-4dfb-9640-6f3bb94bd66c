<template>
  <div class="inventory-table">
    <div class="table-header">
      <div class="title-section">
        <span class="title">库存总量</span>
      </div>
      <div class="action-buttons">
        <el-button type="primary" size="mini" icon="el-icon-search">查询</el-button>
        <el-button size="mini" icon="el-icon-refresh">重置</el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchParams" :inline="true" size="small" class="form_box">
        <el-form-item label="入库单号">
          <el-input v-model="searchParams.inventoryCode" placeholder="请输入产品编码" style="width: 200px;"></el-input>
        </el-form-item>
        <el-form-item label="入库类型">
          <el-select v-model="searchParams.inventoryType" placeholder="请选择" style="width: 200px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="入急冻库" value="11"></el-option>
            <el-option label="入成品库" value="12"></el-option>
            <el-option label="结余入库" value="13"></el-option>
            <el-option label="盘盈入库" value="14"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="入库仓库">
          <el-select v-model="searchParams.warehouseId" placeholder="请选择" style="width: 200px;">
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in warehouseList" :key="item.warehouseId" :label="item.warehouseName"
              :value="item.warehouseId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchParams.storeAgeType" placeholder="请选择" style="width: 200px;">
            <el-option label="正常" value="1"></el-option>
            <el-option label="低库龄" value="2"></el-option>
            <el-option label="呆滞" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格 -->
    <div :style="{ height: tableHeight + 'px' }">
      <el-table :data="tableData" stripe style="width: 100%" border v-loading="loading" :max-height="tableHeight">
        <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
        <el-table-column prop="inventoryCode" align="center" label="入库单号"></el-table-column>
        <el-table-column  align="center" label="入库类型">
          <template slot-scope="scope">
            <span>{{ getInventoryType(scope.row.inventoryType) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="inWarehouseName" align="center" label="入库仓库" show-overflow-tooltip></el-table-column>
        <el-table-column prop="inventoryNum" align="center" label="入库总数量" show-overflow-tooltip></el-table-column>
        <el-table-column prop="inventoryWeight" align="center" label="入库总重量（kg）"
          show-overflow-tooltip></el-table-column>
        <el-table-column prop="createUserName" align="center" label="操作人" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" align="center" label="入库时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="storeAgeNum" align="center" label="库龄" show-overflow-tooltip></el-table-column>
        <el-table-column prop="status" align="center" label="状态" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="getStatusClass(scope.row.storeAgeType)">
              {{ getStatusText(scope.row.storeAgeType) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-box">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="pagination.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InventoryTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: Object,
      default: () => ({
        pageNum: 1,
        pageSize: 10,
        total: 0
      })
    },
    warehouseList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableHeight: 400,
      searchParams: {
        inventoryCode: '',
        inventoryType: '',

        warehouseId: '',
        status: ''
      }
    };
  },
  mounted() {
    this.setTableHeight();
    window.addEventListener('resize', this.setTableHeight);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        const windowHeight = window.innerHeight;
        this.tableHeight = windowHeight - 500; // 根据实际情况调整
        if (this.tableHeight < 300) {
          this.tableHeight = 300;
        }
      });
    },

    handleSizeChange(val) {
      this.$emit('onPageSizeChange', val);
    },

    handleCurrentChange(val) {
      this.$emit('onPageChange', val);
    },

    handleDetail(row) {
      this.$emit('onDetail', row);
    },

    getStockLevelClass(stockLevel) {
      const level = parseInt(stockLevel);
      if (level > 80) return 'stock-high';
      if (level >= 50) return 'stock-medium';
      return 'stock-low';
    },

    getStatusClass(status) {
      if (!status) return '-';
      const statusMap = {
        1: 'status-expired',
        2: 'status-expiring',
        3: 'status-normal'
      }
      return statusMap[status] || 'status-normal';
    },

    getStatusText(status) {
      if (!status) return '-';
      const statusMap = {
        1: '正常',
        2: '低库龄预警',
        3: '呆滞',
      }
      return statusMap[status] || '-'
    },
    getInventoryType(type) {
      if (!type) return '-';
      const typeMap = {
        '11': '入急冻库',
        '12': '入成品库',
        '13': '结余入库',
        '14': '盘盈入库',
      };
      return typeMap[type] || '-';
    },

    // 格式化重量显示
    formatWeight(weight) {
      if (!weight) return '0.00';
      const num = parseFloat(weight);
      if (num >= 1000) {
        return (num / 1000).toFixed(2) + 'T';
      }
      return num.toFixed(2);
    }
  }
};
</script>

<style lang="scss" scoped>
.inventory-table {
  background: white;
  border-radius: 8px;
  padding: 20px;

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #165DFF;
    }

    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .search-form {
    margin-bottom: 20px;
    padding: 16px;
    background: #F7F8FA;
    border-radius: 6px;
  }

  .pagination-box {
    margin-top: 20px;
    text-align: right;
  }

  // 状态样式
  .stock-high {
    color: #F53F3F;
    font-weight: 500;
  }

  .stock-medium {
    color: #FF7D00;
    font-weight: 500;
  }

  .stock-low {
    color: #00B42A;
    font-weight: 500;
  }

  .status-normal {
    color: #00B42A;
    font-weight: 500;
  }

  .status-warning {
    color: #FF7D00;
    font-weight: 500;
  }
}
</style>
